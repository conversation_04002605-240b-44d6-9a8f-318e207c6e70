# -*- coding: utf-8 -*-
"""
网格渲染模块

提供3D网格加载、渲染和材质管理功能
"""

import os
import numpy as np
import trimesh
import pyqtgraph.opengl as gl
from functools import lru_cache
from typing import Dict, List, Optional, Tuple
import stl


class MeshRenderer:
    """3D网格渲染器"""

    def __init__(self, view_widget):
        """
        初始化网格渲染器

        Args:
            view_widget: OpenGL视图组件
        """
        self.view_widget = view_widget
        self.mesh_items = []  # 3D网格显示项列表
        self.mesh_data = {}   # 网格数据缓存
        self.vertex_cache = {}  # 顶点缓存
        self.visual_transforms = {}  # 视觉变换缓存

    def clear_meshes(self):
        """清除所有网格"""
        for item in self.mesh_items:
            self.view_widget.removeItem(item)
        self.mesh_items = []
        self.mesh_data = {}
        self.vertex_cache = {}
        self.visual_transforms = {}

    @lru_cache(maxsize=128)
    def load_mesh_file(self, mesh_path: str) -> Optional[Tuple[np.ndarray, np.ndarray]]:
        """
        加载网格文件（带缓存）

        Args:
            mesh_path (str): 网格文件路径

        Returns:
            Optional[Tuple[np.ndarray, np.ndarray]]: (顶点, 面) 或 None
        """
        if not os.path.exists(mesh_path):
            print(f"网格文件不存在: {mesh_path}")
            return None

        try:
            # 策略1：优先使用trimesh库，支持多种格式（STL, OBJ, PLY等）
            try:
                mesh = trimesh.load(mesh_path)
                return mesh.vertices, mesh.faces
            except:
                # 策略2：如果trimesh失败，对STL文件使用numpy-stl库
                if mesh_path.lower().endswith('.stl'):
                    mesh = stl.mesh.Mesh.from_file(mesh_path)
                    # 将三角形向量重塑为顶点数组
                    vertices = mesh.vectors.reshape(-1, 3)
                    # 为每个顶点创建面索引
                    faces = np.arange(len(vertices)).reshape(-1, 3)
                    return vertices, faces
                else:
                    # 不支持的文件格式
                    raise
        except Exception as e:
            print(f"加载网格错误 {mesh_path}: {str(e)}")
            return None

    def create_mesh_item(self, vertices: np.ndarray, faces: np.ndarray, 
                        color: Tuple[float, float, float, float] = (0.7, 0.7, 0.7, 1.0)) -> gl.GLMeshItem:
        """
        创建网格显示项

        Args:
            vertices (np.ndarray): 顶点数组
            faces (np.ndarray): 面索引数组
            color (Tuple): RGBA颜色

        Returns:
            gl.GLMeshItem: 网格显示项
        """
        try:
            # 创建网格数据
            mesh_data = gl.MeshData(vertexes=vertices, faces=faces)
            
            # 创建网格显示项
            mesh_item = gl.GLMeshItem(
                meshdata=mesh_data,
                smooth=True,
                color=color,
                shader='shaded',
                glOptions='opaque'
            )

            # 存储原始顶点和面数据，用于后续变换
            mesh_item._original_vertices = vertices.copy()
            mesh_item._original_faces = faces.copy()

            return mesh_item

        except Exception as e:
            print(f"创建网格显示项失败: {e}")
            return None

    def apply_transform_to_mesh(self, mesh_item: gl.GLMeshItem, transform: np.ndarray):
        """
        应用变换矩阵到网格

        Args:
            mesh_item (gl.GLMeshItem): 网格显示项
            transform (np.ndarray): 4x4变换矩阵
        """
        if mesh_item is not None and hasattr(mesh_item, '_original_vertices'):
            # 获取原始顶点数据
            vertices = mesh_item._original_vertices
            faces = mesh_item._original_faces

            # 计算变换后的顶点
            vertices_homogeneous = np.hstack((vertices, np.ones((vertices.shape[0], 1))))
            vertices_transformed = np.dot(vertices_homogeneous, transform.T)[:, :3]

            # 更新网格显示数据
            mesh_item.setMeshData(vertexes=vertices_transformed, faces=faces)

    def get_mesh_color_from_material(self, link_name: str, urdf_materials: Dict, 
                                   link_materials: Dict, default_color: Tuple) -> Tuple:
        """
        从URDF材质信息获取网格颜色

        Args:
            link_name (str): 链接名称
            urdf_materials (Dict): URDF材质定义
            link_materials (Dict): 链接材质映射
            default_color (Tuple): 默认颜色

        Returns:
            Tuple: RGBA颜色元组
        """
        # 检查链接是否有特定材质
        if link_name in link_materials:
            material_info = link_materials[link_name]
            if 'color' in material_info:
                color = material_info['color']
                if len(color) >= 3:
                    return tuple(color[:4]) if len(color) >= 4 else tuple(color[:3]) + (1.0,)
            
            # 检查材质名称是否在全局材质定义中
            material_name = material_info.get('name', '')
            if material_name in urdf_materials:
                material_def = urdf_materials[material_name]
                if 'color' in material_def:
                    color = material_def['color']
                    if len(color) >= 3:
                        return tuple(color[:4]) if len(color) >= 4 else tuple(color[:3]) + (1.0,)

        return default_color

    def update_mesh_material_properties(self, mesh_item: gl.GLMeshItem, 
                                      material_type: str, properties: Dict):
        """
        更新网格材质属性

        Args:
            mesh_item (gl.GLMeshItem): 网格显示项
            material_type (str): 材质类型
            properties (Dict): 材质属性
        """
        if mesh_item is None:
            return

        try:
            # 根据材质类型设置不同的渲染选项
            if material_type == "Metallic":
                mesh_item.setShader('shaded')
                mesh_item.setGLOptions('opaque')
            elif material_type == "Glass":
                transparency = properties.get('transparency', 0) / 100.0
                if transparency > 0:
                    mesh_item.setGLOptions('translucent')
                else:
                    mesh_item.setGLOptions('opaque')
            elif material_type == "Plastic":
                mesh_item.setShader('normalColor')
                mesh_item.setGLOptions('opaque')
            else:  # Default
                mesh_item.setShader('shaded')
                mesh_item.setGLOptions('opaque')

        except Exception as e:
            print(f"更新材质属性失败: {e}")

    def toggle_wireframe_mode(self, mesh_item: gl.GLMeshItem, wireframe: bool):
        """
        切换线框模式

        Args:
            mesh_item (gl.GLMeshItem): 网格显示项
            wireframe (bool): 是否启用线框模式
        """
        if mesh_item is None:
            return

        try:
            if wireframe:
                mesh_item.setShader('wireframe')
            else:
                mesh_item.setShader('shaded')
        except Exception as e:
            print(f"切换线框模式失败: {e}")

    def set_mesh_color(self, mesh_item: gl.GLMeshItem, color: Tuple):
        """
        设置网格颜色

        Args:
            mesh_item (gl.GLMeshItem): 网格显示项
            color (Tuple): RGBA颜色
        """
        if mesh_item is None:
            return

        try:
            mesh_item.setColor(color)
        except Exception as e:
            print(f"设置网格颜色失败: {e}")

    def add_mesh_to_scene(self, mesh_item: gl.GLMeshItem):
        """
        将网格添加到场景

        Args:
            mesh_item (gl.GLMeshItem): 网格显示项
        """
        if mesh_item is not None:
            self.view_widget.addItem(mesh_item)
            self.mesh_items.append(mesh_item)

    def remove_mesh_from_scene(self, mesh_item: gl.GLMeshItem):
        """
        从场景移除网格

        Args:
            mesh_item (gl.GLMeshItem): 网格显示项
        """
        if mesh_item is not None and mesh_item in self.mesh_items:
            self.view_widget.removeItem(mesh_item)
            self.mesh_items.remove(mesh_item)
