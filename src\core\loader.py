# -*- coding: utf-8 -*-
"""
URDF加载模块

提供后台线程加载URDF文件的功能
"""

import os
from PySide6.QtCore import QThread, Signal
from urdf_parser_py.urdf import URDF
from .urdf_parser import URDFPathConverter


class URDFLoadWorker(QThread):
    """
    URDF加载工作线程

    在后台线程中执行URDF文件的加载和解析操作，避免阻塞主界面线程。
    通过信号与主线程通信，报告加载进度和结果。
    """

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    loading_finished = Signal(object, str, str)  # 加载完成信号 (robot, urdf_dir, package_dir)
    loading_failed = Signal(str)  # 加载失败信号

    def __init__(self, file_path, workspace_root):
        """
        初始化工作线程

        Args:
            file_path (str): URDF文件路径
            workspace_root (str): 工作空间根目录
        """
        super().__init__()
        print(222, workspace_root)
        self.file_path = file_path
        self.workspace_root = workspace_root
        self.robot = None
        self.urdf_dir = None
        self.package_dir = None

    def run(self):
        """
        线程主执行函数

        在后台线程中执行URDF加载的所有耗时操作
        """
        try:
            self.progress_updated.emit("正在初始化路径转换器...")

            # 初始化路径转换器
            path_converter = URDFPathConverter(self.workspace_root)

            self.progress_updated.emit("正在检查URDF文件...")

            # 检查URDF文件是否包含需要转换的ROS package路径
            converted_urdf_path = self.file_path
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "package://" in content:
                    self.progress_updated.emit("检测到ROS package路径，正在转换...")
                    output_path = self.file_path.replace('.urdf', '_converted.urdf')
                    converted_urdf_path = path_converter.convert_urdf_file(self.file_path, output_path)

            # 存储重要的目录路径信息
            self.urdf_dir = os.path.dirname(os.path.abspath(self.file_path))
            self.package_dir = os.path.dirname(self.urdf_dir)

            self.progress_updated.emit("正在解析URDF文件...")

            # 解析URDF文件，创建机器人模型对象
            self.robot = URDF.from_xml_file(converted_urdf_path)

            self.progress_updated.emit(f"成功解析URDF文件。找到 {len(self.robot.links)} 个链接。")

            # 发送加载完成信号
            self.loading_finished.emit(self.robot, self.urdf_dir, self.package_dir)

        except Exception as e:
            error_msg = f"加载URDF文件失败: {str(e)}"
            print(error_msg)
            import traceback
            print("错误追踪:")
            print(traceback.format_exc())
            self.loading_failed.emit(error_msg)
