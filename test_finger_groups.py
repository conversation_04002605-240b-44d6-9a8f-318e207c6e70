#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的手指分组功能

这个脚本用于测试 _analyze_finger_groups 函数的自动关节识别功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from core.urdf_parser import URDFJointAnalyzer, FingerGroupAnalyzer


def test_finger_groups(urdf_path: str):
    """
    测试手指分组功能
    
    Args:
        urdf_path (str): URDF文件路径
    """
    print(f"测试URDF文件: {urdf_path}")
    print("=" * 60)
    
    try:
        # 创建关节分析器
        joint_analyzer = URDFJointAnalyzer(urdf_path)
        
        # 打印所有可控关节
        print("\n=== 所有可控关节 ===")
        controllable_joints = joint_analyzer.get_controllable_joints()
        for joint_name, info in controllable_joints.items():
            print(f"  {joint_name}: {info['type']}")
        
        # 创建手指分组分析器
        finger_analyzer = FingerGroupAnalyzer(joint_analyzer)
        
        # 获取分组结果
        finger_groups = finger_analyzer.get_finger_groups()
        
        # 打印手指分组结果
        print("\n=== 自动检测的手指分组 ===")
        for group_key, group_config in finger_groups['finger_groups'].items():
            print(f"\n{group_key} ({group_config['name']}):")
            print(f"  描述: {group_config['description']}")
            print(f"  关节: {group_config['joints']}")
            print(f"  比例: {group_config['ratios']}")
        
        # 打印独立关节
        print("\n=== 独立关节 ===")
        for joint_name in finger_groups['individual_joints'].keys():
            print(f"  {joint_name}")
        
        print("\n" + "=" * 60)
        print("测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) != 2:
        print("用法: python test_finger_groups.py <urdf_file_path>")
        print("示例: python test_finger_groups.py robot.urdf")
        return
    
    urdf_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(urdf_path):
        print(f"错误: URDF文件不存在: {urdf_path}")
        return
    
    # 运行测试
    test_finger_groups(urdf_path)


if __name__ == "__main__":
    main()
