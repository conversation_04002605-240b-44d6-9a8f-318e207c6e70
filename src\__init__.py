# -*- coding: utf-8 -*-
"""
URDF整合查看器模块包

该包提供了一个完整的URDF文件查看和交互系统，包括：
- URDF文件解析和路径转换
- 3D网格模型加载和显示
- 智能关节分组控制
- 实时运动学计算
- 材质和视觉效果控制

模块结构：
- core: 核心业务逻辑模块
- ui: 用户界面模块
- rendering: 3D渲染模块
- utils: 工具和辅助函数模块
"""

__version__ = "1.0.0"
__author__ = "URDF Viewer Team"

# 导入主要类
from .core.urdf_parser import URDFPathConverter, URDFJointAnalyzer, FingerGroupAnalyzer
from .core.kinematics import SimpleTransformCalculator
from .core.loader import URDFLoadWorker
from .ui.main_window import URDFIntegratedViewer
from .ui.control_panels import FileControlPanel, CameraControlPanel, MaterialControlPanel, ColorControlPanel
from .ui.joint_controls import JointControlManager
from .rendering.mesh_renderer import MeshRenderer
from .rendering.camera_controller import CameraController
from .utils.constants import *

__all__ = [
    'URDFPathConverter',
    'URDFJointAnalyzer',
    'FingerGroupAnalyzer',
    'SimpleTransformCalculator',
    'URDFLoadWorker',
    'URDFIntegratedViewer',
    'FileControlPanel',
    'CameraControlPanel',
    'MaterialControlPanel',
    'ColorControlPanel',
    'JointControlManager',
    'MeshRenderer',
    'CameraController'
]