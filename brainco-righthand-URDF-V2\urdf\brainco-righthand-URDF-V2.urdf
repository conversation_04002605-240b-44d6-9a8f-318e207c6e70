<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="brainco-righthand-URDF-V2">
  <link
    name="right_base_link">
    <inertial>
      <origin
        xyz="-0.00524378149482414 -0.00340380216679143 0.0521266764483739"
        rpy="0 0 0" />
      <mass
        value="0.028376369561293" />
      <inertia
        ixx="2.10911661425889E-05"
        ixy="3.8632162614135E-08"
        ixz="9.36399509676338E-08"
        iyy="1.44621912098366E-05"
        iyz="-1.09576528340187E-06"
        izz="7.99811473962489E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_base_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_base_link.STL" />
      </geometry>
    </collision>
  </link>
  <link
    name="right_thumb_metacarpal_link">
    <inertial>
      <origin
        xyz="-1.51105893291988E-05 0.00707242532016975 0.00420847293427094"
        rpy="0 0 0" />
      <mass
        value="0.00116019531148996" />
      <inertia
        ixx="1.59239837111329E-07"
        ixy="4.69100836631313E-09"
        ixz="4.92933775261591E-10"
        iyy="1.60913456568321E-07"
        iyz="1.48146725871811E-08"
        izz="5.13550255314669E-08" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_metacarpal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_metacarpal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_thumb_metacarpal_joint"
    type="revolute">
    <origin
      xyz="0.005619 0.019867 0.027825"
      rpy="3.1416 0 2.9737" />
    <parent
      link="right_base_link" />
    <child
      link="right_thumb_metacarpal_link" />
    <axis
      xyz="0 0 1" />
    <limit
      lower="0"
      upper="1.5184"
      effort="0.5"
      velocity="2.6175" />
  </joint>
  <link
    name="right_thumb_proximal_link">
    <inertial>
      <origin
        xyz="-4.57172872246614E-07 0.0250090110844873 -0.00175049403575901"
        rpy="0 0 0" />
      <mass
        value="0.013437308865305" />
      <inertia
        ixx="3.23319869389856E-06"
        ixy="-1.19075992087737E-10"
        ixz="1.24443381456471E-11"
        iyy="5.64506002356025E-07"
        iyz="5.04129419740443E-08"
        izz="3.39928434904953E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_proximal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_proximal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_thumb_proximal_joint"
    type="revolute">
    <origin
      xyz="0 0.014227 0"
      rpy="-2.9667 -0.23736 2.9737" />
    <parent
      link="right_thumb_metacarpal_link" />
    <child
      link="right_thumb_proximal_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.0472"
      effort="1.1"
      velocity="2.5303" />
  </joint>
  <link
    name="right_thumb_distal_link">
    <inertial>
      <origin
        xyz="-5.88258313588552E-09 0.0117544716247142 1.13324397438969E-05"
        rpy="0 0 0" />
      <mass
        value="0.00417181579838271" />
      <inertia
        ixx="1.67793545309794E-07"
        ixy="7.15927847824234E-12"
        ixz="-6.8009537088991E-12"
        iyy="1.15362128819331E-07"
        iyz="1.2517499089377E-08"
        izz="2.19553178338424E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_distal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_distal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_thumb_distal_joint"
    type="revolute">
    <origin
      xyz="0 0.052 0"
      rpy="0 0 0" />
    <parent
      link="right_thumb_proximal_link" />
    <child
      link="right_thumb_distal_link" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="1.0472"
      effort="1.1"
      velocity="2.5303" />
  </joint>
  <link
    name="right_thumb_tip">
    <inertial>
      <origin
        xyz="6.93889390390723E-18 -8.79922039356273E-05 1.73472347597681E-18"
        rpy="0 0 0" />
      <mass
        value="6.54498469497874E-08" />
      <inertia
        ixx="1.63624617374468E-15"
        ixy="3.08148791101958E-33"
        ixz="2.46519032881566E-32"
        iyy="1.63624617374468E-15"
        iyz="1.07852076885685E-32"
        izz="1.63624617374468E-15" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_tip.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_tip.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_thumb_tip"
    type="revolute">
    <origin
      xyz="0 0.026529 -0.0017734"
      rpy="-0.06675 0 0" />
    <parent
      link="right_thumb_distal_link" />
    <child
      link="right_thumb_tip" />
    <axis
      xyz="1 0 0" />
    <limit
      lower="0"
      upper="0"
      effort="0"
      velocity="0" />
  </joint>
  <link
    name="right_thumb_touch_Link">
    <inertial>
      <origin
        xyz="-1.1806E-08 -0.014861 0.00079531"
        rpy="0 0 0" />
      <mass
        value="0.0041718" />
      <inertia
        ixx="1.6779E-07"
        ixy="7.5595E-12"
        ixz="-6.3854E-12"
        iyy="1.1416E-07"
        iyz="5.472E-09"
        izz="2.2076E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_touch_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.53 0.81 0.92 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_thumb_touch_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_thumb_touch_joint"
    type="fixed">
    <origin
      xyz="0 0.026529 -0.0017734"
      rpy="-0.06675 0 0" />
    <parent
      link="right_thumb_distal_link" />
    <child
      link="right_thumb_touch_Link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="right_index_proximal_link">
    <inertial>
      <origin
        xyz="-0.00223495193199565 3.15874577787312E-06 0.0176402020341366"
        rpy="0 0 0" />
      <mass
        value="0.00195502251290869" />
      <inertia
        ixx="2.51945688372283E-07"
        ixy="1.19772964297278E-11"
        ixz="-6.37275644668917E-09"
        iyy="2.36435336139899E-07"
        iyz="1.65019165740596E-10"
        izz="9.30351046594029E-08" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_index_proximal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_index_proximal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_index_proximal_joint"
    type="revolute">
    <origin
      xyz="-0.0021181 0.029568 0.080876"
      rpy="-0.11819 0.14743 -0.17422" />
    <parent
      link="right_base_link" />
    <child
      link="right_index_proximal_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="1.4661"
      effort="2"
      velocity="2.2685" />
  </joint>
  <link
    name="right_index_distal_link">
    <inertial>
      <origin
        xyz="0.00330169965345207 1.80502678040484E-07 0.0142076903844267"
        rpy="0 0 0" />
      <mass
        value="0.00369310711438786" />
      <inertia
        ixx="3.39968050760851E-07"
        ixy="-2.72439842925838E-12"
        ixz="-8.75041192697868E-08"
        iyy="3.62244794159632E-07"
        iyz="-3.69168934116956E-12"
        izz="9.1034511229316E-08" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_index_distal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_index_distal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_index_distal_joint"
    type="revolute">
    <origin
      xyz="0 0 0.032"
      rpy="0 0 0" />
    <parent
      link="right_index_proximal_link" />
    <child
      link="right_index_distal_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="1.693"
      effort="2"
      velocity="2.2685" />
  </joint>
  <link
    name="right_index_tip">
    <inertial>
      <origin
        xyz="0 6.93889390390723E-18 2.77555756156289E-17"
        rpy="0 0 0" />
      <mass
        value="6.54498469497874E-08" />
      <inertia
        ixx="1.63624617374468E-15"
        ixy="-4.93038065763132E-32"
        ixz="0"
        iyy="1.63624617374468E-15"
        iyz="-1.23259516440783E-32"
        izz="1.63624617374468E-15" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_index_tip.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_index_tip.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_index_tip_joint"
    type="revolute">
    <origin
      xyz="0.011097 0 0.035739"
      rpy="0 0.4756 0" />
    <parent
      link="right_index_distal_link" />
    <child
      link="right_index_tip" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="0"
      effort="0"
      velocity="0" />
  </joint>
  <link
    name="right_index_touch_Link">
    <inertial>
      <origin
        xyz="0.0057175 4.0322E-08 -0.0069766"
        rpy="0 0 0" />
      <mass
        value="0.00037027" />
      <inertia
        ixx="6.7769E-09"
        ixy="-7.0394E-14"
        ixz="1.5149E-09"
        iyy="5.4134E-09"
        iyz="1.697E-13"
        izz="3.1925E-09" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_index_touch_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.53 0.81 0.92 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_index_touch_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_index_touch_joint"
    type="fixed">
    <origin
      xyz="0.011097 0 0.035739"
      rpy="0 0.4756 0" />
    <parent
      link="right_index_distal_link" />
    <child
      link="right_index_touch_Link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="right_middle_proximal_link">
    <inertial>
      <origin
        xyz="-0.00222511101223747 5.25632286623237E-06 0.0203007776295123"
        rpy="0 0 0" />
      <mass
        value="0.00232228766449837" />
      <inertia
        ixx="3.6699623719538E-07"
        ixy="2.6834089939559E-11"
        ixz="-9.79836587937101E-09"
        iyy="3.52645347928613E-07"
        iyz="2.57865582739809E-10"
        izz="1.12233932527467E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_middle_proximal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_middle_proximal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_middle_proximal_joint"
    type="revolute">
    <origin
      xyz="-0.0045767 0.010051 0.084993"
      rpy="-0.039468 0.1483 -0.057975" />
    <parent
      link="right_base_link" />
    <child
      link="right_middle_proximal_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="1.4661"
      effort="2"
      velocity="2.2685" />
  </joint>
  <link
    name="right_middle_distal_link">
    <inertial>
      <origin
        xyz="0.00400815246049972 -2.93852058033672E-08 0.0164973984498919"
        rpy="0 0 0" />
      <mass
        value="0.00446018836116639" />
      <inertia
        ixx="5.12550471641205E-07"
        ixy="2.49574461243601E-12"
        ixz="-1.36569370373267E-07"
        iyy="5.4982769389762E-07"
        iyz="1.1510593712835E-12"
        izz="1.23560211132881E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_middle_distal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_middle_distal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_middle_distal_joint"
    type="revolute">
    <origin
      xyz="0 0 0.037"
      rpy="0 0 0" />
    <parent
      link="right_middle_proximal_link" />
    <child
      link="right_middle_distal_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="1.693"
      effort="2"
      velocity="2.2685" />
  </joint>
  <link
    name="right_middle_tip">
    <inertial>
      <origin
        xyz="0 -3.46944695195361E-18 0"
        rpy="0 0 0" />
      <mass
        value="6.54498469497874E-08" />
      <inertia
        ixx="1.63624617374468E-15"
        ixy="-6.16297582203915E-33"
        ixz="9.86076131526265E-32"
        iyy="1.63624617374468E-15"
        iyz="3.08148791101958E-33"
        izz="1.63624617374468E-15" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_middle_tip.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_middle_tip.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_middle_tip_joint"
    type="revolute">
    <origin
      xyz="0.012558 0 0.040442"
      rpy="0 0.4756 0" />
    <parent
      link="right_middle_distal_link" />
    <child
      link="right_middle_tip" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="0"
      effort="0"
      velocity="0" />
  </joint>
  <link
    name="right_middle_touch_Link">
    <inertial>
      <origin
        xyz="0.0061888 -1.9225E-08 -0.0077714"
        rpy="0 0 0" />
      <mass
        value="0.00045875" />
      <inertia
        ixx="1.0049E-08"
        ixy="-7.8705E-14"
        ixz="2.2592E-09"
        iyy="8.2323E-09"
        iyz="1.5083E-13"
        izz="4.4127E-09" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_middle_touch_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.53 0.81 0.92 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_middle_touch_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_middle_touch_joint"
    type="fixed">
    <origin
      xyz="0.012558 0 0.040442"
      rpy="0 0.4756 0" />
    <parent
      link="right_middle_distal_link" />
    <child
      link="right_middle_touch_Link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="right_ring_proximal_link">
    <inertial>
      <origin
        xyz="-0.00234494526421452 5.87954755615957E-06 0.019177402549597"
        rpy="0 0 0" />
      <mass
        value="0.00214992998311427" />
      <inertia
        ixx="3.14177684848335E-07"
        ixy="3.34382331089778E-11"
        ixz="-7.7898178089218E-09"
        iyy="2.97613170150896E-07"
        iyz="2.41251871803827E-10"
        izz="1.0211543233675E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_ring_proximal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_ring_proximal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_ring_proximal_joint"
    type="revolute">
    <origin
      xyz="-0.0046709 -0.010037 0.083982"
      rpy="0.039462 0.14721 0.057932" />
    <parent
      link="right_base_link" />
    <child
      link="right_ring_proximal_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="1.4661"
      effort="2"
      velocity="2.2685" />
  </joint>
  <link
    name="right_ring_distal_link">
    <inertial>
      <origin
        xyz="0.00382459959210406 7.80178744906601E-08 0.0160343764410312"
        rpy="0 0 0" />
      <mass
        value="0.00426161250233792" />
      <inertia
        ixx="4.71792497420504E-07"
        ixy="-6.95225675138545E-13"
        ixz="-1.26948440940938E-07"
        iyy="5.06071998616974E-07"
        iyz="-2.19413968774491E-12"
        izz="1.15762768100302E-07" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_ring_distal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_ring_distal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_ring_distal_joint"
    type="revolute">
    <origin
      xyz="0 0 0.035"
      rpy="0 0 0" />
    <parent
      link="right_ring_proximal_link" />
    <child
      link="right_ring_distal_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="1.693"
      effort="2"
      velocity="2.2685" />
  </joint>
  <link
    name="right_ring_tip">
    <inertial>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <mass
        value="6.54498469497874E-08" />
      <inertia
        ixx="1.63624617374468E-15"
        ixy="-6.16297582203915E-33"
        ixz="9.86076131526265E-32"
        iyy="1.63624617374468E-15"
        iyz="0"
        izz="1.63624617374468E-15" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_ring_tip.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_ring_tip.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_ring_tip_joint"
    type="revolute">
    <origin
      xyz="0.012266 0 0.039501"
      rpy="0 0.4756 0" />
    <parent
      link="right_ring_distal_link" />
    <child
      link="right_ring_tip" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="0"
      effort="0"
      velocity="0" />
  </joint>
  <link
    name="right_ring_touch_Link">
    <inertial>
      <origin
        xyz="0.0059543 -4.4258E-08 -0.0074592"
        rpy="0 0 0" />
      <mass
        value="0.00040139" />
      <inertia
        ixx="8.2018E-09"
        ixy="-2.633E-14"
        ixz="1.8321E-09"
        iyy="6.6365E-09"
        iyz="1.1253E-13"
        izz="3.6193E-09" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_ring_touch_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.53 0.81 0.92 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_ring_touch_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_ring_touch_joint"
    type="fixed">
    <origin
      xyz="0.012266 0 0.039501"
      rpy="0 0.4756 0" />
    <parent
      link="right_ring_distal_link" />
    <child
      link="right_ring_touch_Link" />
    <axis
      xyz="0 0 0" />
  </joint>
  <link
    name="right_pinky_proximal_link">
    <inertial>
      <origin
        xyz="-0.00213212021184178 7.75039446098032E-06 0.0160671583560383"
        rpy="0 0 0" />
      <mass
        value="0.00173854673404663" />
      <inertia
        ixx="1.97563213550085E-07"
        ixy="2.23402433057963E-11"
        ixz="-4.5842566369208E-09"
        iyy="1.82023043083307E-07"
        iyz="1.08028441152492E-10"
        izz="8.21624712033168E-08" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_pinky_proximal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_pinky_proximal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_pinky_proximal_joint"
    type="revolute">
    <origin
      xyz="-0.0023566 -0.029366 0.078694"
      rpy="0.11815 0.14491 0.17392" />
    <parent
      link="right_base_link" />
    <child
      link="right_pinky_proximal_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="1.4661"
      effort="2"
      velocity="2.2685" />
  </joint>
  <link
    name="right_pinky_distal_link">
    <inertial>
      <origin
        xyz="0.00307974197235919 -1.54572112227858E-07 0.0130246967256256"
        rpy="0 0 0" />
      <mass
        value="0.00338416876348922" />
      <inertia
        ixx="2.70050429038431E-07"
        ixy="1.68029750999903E-12"
        ixz="-6.70780254430148E-08"
        iyy="2.85275081521347E-07"
        iyz="5.10635117768131E-12"
        izz="7.90798706854226E-08" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_pinky_distal_link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_pinky_distal_link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_pinky_distal_joint"
    type="revolute">
    <origin
      xyz="0 0 0.029"
      rpy="0 0 0" />
    <parent
      link="right_pinky_proximal_link" />
    <child
      link="right_pinky_distal_link" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="1.693"
      effort="2"
      velocity="2.2685" />
  </joint>
  <link
    name="right_pinky_tip">
    <inertial>
      <origin
        xyz="3.46944695195361E-18 -5.20417042793042E-18 0"
        rpy="0 0 0" />
      <mass
        value="6.54498469497874E-08" />
      <inertia
        ixx="1.63624617374468E-15"
        ixy="0"
        ixz="0"
        iyy="1.63624617374468E-15"
        iyz="4.93038065763132E-32"
        izz="1.63624617374468E-15" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_pinky_tip.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_pinky_tip.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_pinky_tip_joint"
    type="revolute">
    <origin
      xyz="0.010221 0 0.032918"
      rpy="0 0.4756 0" />
    <parent
      link="right_pinky_distal_link" />
    <child
      link="right_pinky_tip" />
    <axis
      xyz="0 1 0" />
    <limit
      lower="0"
      upper="0"
      effort="0"
      velocity="0" />
  </joint>
  <link
    name="right_pinky_touch_Link">
    <inertial>
      <origin
        xyz="0.0058772 -2.0056E-07 -0.0070961"
        rpy="0 0 0" />
      <mass
        value="0.00037341" />
      <inertia
        ixx="6.9191E-09"
        ixy="1.2176E-13"
        ixz="1.4275E-09"
        iyy="5.3436E-09"
        iyz="7.1635E-14"
        izz="3.2829E-09" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_pinky_touch_Link.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.53 0.81 0.92 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://brainco-righthand-URDF-V2/meshes/right_pinky_touch_Link.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="right_pinky_touch_joint"
    type="fixed">
    <origin
      xyz="0.010221 0 0.032918"
      rpy="0 0.4756 0" />
    <parent
      link="right_pinky_distal_link" />
    <child
      link="right_pinky_touch_Link" />
    <axis
      xyz="0 0 0" />
  </joint>
</robot>