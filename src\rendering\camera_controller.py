# -*- coding: utf-8 -*-
"""
相机控制模块

提供3D视图相机控制功能
"""

import numpy as np
from typing import Dict, Tuple
from ..utils.constants import CAMERA_PRESETS


class CameraController:
    """3D视图相机控制器"""

    def __init__(self, view_widget):
        """
        初始化相机控制器

        Args:
            view_widget: OpenGL视图组件
        """
        self.view_widget = view_widget
        self.camera_state = {
            'distance': 10.0,
            'elevation': 30.0,
            'azimuth': 45.0,
            'center': [0, 0, 0]
        }

    def set_camera_parameters(self, distance: float = None, azimuth: float = None, 
                            elevation: float = None, center_x: float = None,
                            center_y: float = None, center_z: float = None):
        """
        设置相机参数

        Args:
            distance (float): 相机距离
            azimuth (float): 方位角
            elevation (float): 仰角
            center_x (float): 中心X坐标
            center_y (float): 中心Y坐标
            center_z (float): 中心Z坐标
        """
        if distance is not None:
            self.camera_state['distance'] = distance
        if azimuth is not None:
            self.camera_state['azimuth'] = azimuth
        if elevation is not None:
            self.camera_state['elevation'] = elevation
        if center_x is not None:
            self.camera_state['center'][0] = center_x
        if center_y is not None:
            self.camera_state['center'][1] = center_y
        if center_z is not None:
            self.camera_state['center'][2] = center_z

        self._update_camera_view()

    def _update_camera_view(self):
        """更新相机视图"""
        try:
            from PySide6.QtGui import QVector3D

            # 设置相机位置和中心点
            center = self.camera_state['center']
            self.view_widget.setCameraPosition(
                distance=self.camera_state['distance'],
                pos=QVector3D(center[0], center[1], center[2])
            )

            # 设置相机参数
            self.view_widget.setCameraParams(
                elevation=self.camera_state['elevation'],
                azimuth=self.camera_state['azimuth']
            )
        except Exception as e:
            print(f"更新相机视图失败: {e}")

    def reset_camera(self):
        """重置相机到默认位置"""
        self.camera_state = {
            'distance': 10.0,
            'elevation': 30.0,
            'azimuth': 45.0,
            'center': [0, 0, 0]
        }
        self._update_camera_view()

    def set_camera_preset(self, preset_name: str):
        """
        设置相机预设

        Args:
            preset_name (str): 预设名称
        """
        if preset_name in CAMERA_PRESETS:
            preset = CAMERA_PRESETS[preset_name]
            self.set_camera_parameters(
                distance=preset['distance'],
                azimuth=preset['azimuth'],
                elevation=preset['elevation']
            )

    def auto_center_model(self, mesh_items: list):
        """
        自动居中模型

        Args:
            mesh_items (list): 网格项列表
        """
        if not mesh_items:
            return

        try:
            # 计算所有网格的边界框
            all_vertices = []
            for mesh_item in mesh_items:
                if hasattr(mesh_item, 'meshdata') and mesh_item.meshdata is not None:
                    vertices = mesh_item.meshdata.vertexes()
                    if vertices is not None and len(vertices) > 0:
                        # 应用变换矩阵
                        if hasattr(mesh_item, 'transform') and mesh_item.transform is not None:
                            transform = mesh_item.transform.matrix()
                            # 转换为numpy数组并应用变换
                            vertices_4d = np.column_stack([vertices, np.ones(len(vertices))])
                            transformed_vertices = np.dot(vertices_4d, transform.T)[:, :3]
                            all_vertices.extend(transformed_vertices)
                        else:
                            all_vertices.extend(vertices)

            if all_vertices:
                all_vertices = np.array(all_vertices)
                
                # 计算边界框中心
                min_coords = np.min(all_vertices, axis=0)
                max_coords = np.max(all_vertices, axis=0)
                center = (min_coords + max_coords) / 2
                
                # 计算合适的距离
                bbox_size = np.max(max_coords - min_coords)
                distance = bbox_size * 2.5  # 适当的观察距离
                
                # 更新相机参数
                self.set_camera_parameters(
                    distance=distance,
                    center_x=center[0],
                    center_y=center[1],
                    center_z=center[2]
                )
                
                print(f"自动居中: 中心={center}, 距离={distance}")

        except Exception as e:
            print(f"自动居中失败: {e}")

    def get_camera_state(self) -> Dict:
        """
        获取当前相机状态

        Returns:
            Dict: 相机状态字典
        """
        return self.camera_state.copy()

    def orbit_camera(self, delta_azimuth: float, delta_elevation: float):
        """
        环绕相机

        Args:
            delta_azimuth (float): 方位角变化量
            delta_elevation (float): 仰角变化量
        """
        new_azimuth = (self.camera_state['azimuth'] + delta_azimuth) % 360
        new_elevation = max(-90, min(90, self.camera_state['elevation'] + delta_elevation))
        
        self.set_camera_parameters(azimuth=new_azimuth, elevation=new_elevation)

    def zoom_camera(self, zoom_factor: float):
        """
        缩放相机

        Args:
            zoom_factor (float): 缩放因子（>1放大，<1缩小）
        """
        new_distance = max(0.1, self.camera_state['distance'] / zoom_factor)
        self.set_camera_parameters(distance=new_distance)

    def pan_camera(self, delta_x: float, delta_y: float):
        """
        平移相机

        Args:
            delta_x (float): X轴平移量
            delta_y (float): Y轴平移量
        """
        center = self.camera_state['center']
        self.set_camera_parameters(
            center_x=center[0] + delta_x,
            center_y=center[1] + delta_y
        )
