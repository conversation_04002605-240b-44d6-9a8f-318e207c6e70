# -*- coding: utf-8 -*-
"""
常量定义模块

定义应用程序中使用的各种常量
"""

from PySide6.QtGui import QColor

# 默认颜色配置
DEFAULT_COLORS = {
    'link': QColor(180, 180, 180, 255),  # 默认链接颜色（灰色）
    'joint': QColor(255, 0, 0, 255),     # 默认关节颜色（红色）
    'background': (0.15, 0.15, 0.2, 1.0)  # 背景颜色（深蓝灰色）
}

# 材质预设配置
MATERIAL_PRESETS = {
    'chrome': {
        'glossiness': 90,
        'metalness': 100,
        'transparency': 0,
        'material_type': 'Metallic'
    },
    'gold': {
        'glossiness': 80,
        'metalness': 90,
        'transparency': 0,
        'material_type': 'Metallic'
    },
    'plastic': {
        'glossiness': 30,
        'metalness': 0,
        'transparency': 0,
        'material_type': 'Plastic'
    }
}

# 相机预设配置
CAMERA_PRESETS = {
    'front': {'distance': 10.0, 'azimuth': 0, 'elevation': 0},
    'side': {'distance': 10.0, 'azimuth': 90, 'elevation': 0},
    'top': {'distance': 10.0, 'azimuth': 0, 'elevation': 90},
    'bottom': {'distance': 10.0, 'azimuth': 0, 'elevation': -90},
    'back': {'distance': 10.0, 'azimuth': 180, 'elevation': 0},
    'isometric': {'distance': 10.0, 'azimuth': 45, 'elevation': 30}
}

# 手指关节分组模式
FINGER_PATTERNS = {
    'thumb_rotation': {
        'name': '拇指转动',
        'joints': ['thumb_metacarpal_joint', 'right_thumb_metacarpal_joint'],
        'ratios': [1.0, 1.0],
        'description': '控制拇指左右转动（默认外转，对掌运动时内转）'
    },
    'thumb_bend': {
        'name': '拇指弯曲',
        'joints': ['thumb_proximal_joint', 'thumb_distal_joint',
                  'right_thumb_proximal_joint', 'right_thumb_distal_joint'],
        'ratios': [1.0, 0.8, 1.0, 0.8],
        'description': '控制拇指弯曲捏合'
    },
    'index': {
        'name': '食指',
        'joints': ['index_proximal_joint', 'index_distal_joint', 'index_tip_joint',
                  'right_index_proximal_joint', 'right_index_distal_joint', 'right_index_tip_joint'],
        'ratios': [1.0, 0.8, 0.6, 1.0, 0.8, 0.6],
        'description': '控制食指弯曲'
    },
    'middle': {
        'name': '中指',
        'joints': ['middle_proximal_joint', 'middle_distal_joint', 'middle_tip_joint',
                  'right_middle_proximal_joint', 'right_middle_distal_joint', 'right_middle_tip_joint'],
        'ratios': [1.0, 0.8, 0.6, 1.0, 0.8, 0.6],
        'description': '控制中指弯曲'
    },
    'ring': {
        'name': '无名指',
        'joints': ['ring_proximal_joint', 'ring_distal_joint', 'ring_tip_joint',
                  'right_ring_proximal_joint', 'right_ring_distal_joint', 'right_ring_tip_joint'],
        'ratios': [1.0, 0.8, 0.6, 1.0, 0.8, 0.6],
        'description': '控制无名指弯曲'
    },
    'pinky': {
        'name': '小指',
        'joints': ['pinky_proximal_joint', 'pinky_distal_joint', 'pinky_tip_joint',
                  'right_pinky_proximal_joint', 'right_pinky_distal_joint', 'right_pinky_tip_joint'],
        'ratios': [1.0, 0.8, 0.6, 1.0, 0.8, 0.6],
        'description': '控制小指弯曲'
    }
}

# 应用程序配置
APP_CONFIG = {
    'window_title': 'URDF整合查看器 - 显示与控制',
    'window_size': (1600, 1000),
    'min_3d_view_size': (800, 600),
    'control_panel_width': (480, 600),
    'update_interval': 16,  # 60FPS更新频率
    'version': '1.0.0'
}
