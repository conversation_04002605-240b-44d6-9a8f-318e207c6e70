# -*- coding: utf-8 -*-
"""
控制面板模块

包含各种控制面板的实现
"""

from PySide6.QtWidgets import (QGroupBox, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QSlider, QComboBox, QColorDialog, QDoubleSpinBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor
from ..utils.constants import MATERIAL_PRESETS, CAMERA_PRESETS


class FileControlPanel(QGroupBox):
    """文件控制面板"""
    
    load_urdf_requested = Signal()
    save_image_requested = Signal()
    
    def __init__(self):
        super().__init__("文件控制")
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # URDF文件加载按钮
        self.load_button = QPushButton("加载 URDF")
        self.load_button.clicked.connect(self.load_urdf_requested.emit)
        layout.addWidget(self.load_button)
        
        # 图片保存按钮
        self.save_image_button = QPushButton("保存图片")
        self.save_image_button.clicked.connect(self.save_image_requested.emit)
        self.save_image_button.setEnabled(False)
        layout.addWidget(self.save_image_button)
        
        # 状态显示标签
        self.status_label = QLabel("未加载URDF文件")
        layout.addWidget(self.status_label)
    
    def set_loading_state(self, loading: bool):
        """设置加载状态"""
        self.load_button.setEnabled(not loading)
        if loading:
            self.status_label.setText("正在加载...")
    
    def set_status(self, status: str):
        """设置状态文本"""
        self.status_label.setText(status)
    
    def enable_save_image(self, enabled: bool):
        """启用/禁用保存图片按钮"""
        self.save_image_button.setEnabled(enabled)


class CameraControlPanel(QGroupBox):
    """相机控制面板"""
    
    distance_changed = Signal(float)
    azimuth_changed = Signal(float)
    elevation_changed = Signal(float)
    center_x_changed = Signal(float)
    center_y_changed = Signal(float)
    center_z_changed = Signal(float)
    reset_camera_requested = Signal()
    reset_center_requested = Signal()
    auto_center_requested = Signal()
    preset_requested = Signal(str)
    test_requested = Signal(float, float, float)
    
    def __init__(self):
        super().__init__("相机控制")
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 距离控制
        distance_layout = QHBoxLayout()
        distance_layout.addWidget(QLabel("距离:"))
        self.distance_slider = QSlider(Qt.Horizontal)
        self.distance_slider.setMinimum(1)
        self.distance_slider.setMaximum(1000)
        self.distance_slider.setValue(100)
        self.distance_slider.valueChanged.connect(self._on_distance_changed)
        distance_layout.addWidget(self.distance_slider)
        self.distance_value_label = QLabel("10.0")
        distance_layout.addWidget(self.distance_value_label)
        layout.addLayout(distance_layout)
        
        # 测试控件
        test_layout = QHBoxLayout()
        self.spibbox = QDoubleSpinBox()
        self.spibbox.setValue(0.18)
        self.spibbox.setRange(-99999, 9999)
        test_layout.addWidget(self.spibbox)
        
        self.spibbox3 = QDoubleSpinBox()
        self.spibbox3.setValue(0)
        self.spibbox3.setRange(-99999, 9999)
        test_layout.addWidget(self.spibbox3)
        
        self.spibbox2 = QDoubleSpinBox()
        self.spibbox2.setValue(0.12)
        self.spibbox2.setRange(-99999, 9999)
        test_layout.addWidget(self.spibbox2)
        
        self.test_button = QPushButton("测试")
        self.test_button.clicked.connect(self._on_test_clicked)
        test_layout.addWidget(self.test_button)
        layout.addLayout(test_layout)
        
        # 方位角控制
        azimuth_layout = QHBoxLayout()
        azimuth_layout.addWidget(QLabel("方位角:"))
        self.azimuth_slider = QSlider(Qt.Horizontal)
        self.azimuth_slider.setMinimum(0)
        self.azimuth_slider.setMaximum(360)
        self.azimuth_slider.setValue(45)
        self.azimuth_slider.valueChanged.connect(self._on_azimuth_changed)
        azimuth_layout.addWidget(self.azimuth_slider)
        self.azimuth_value_label = QLabel("45°")
        azimuth_layout.addWidget(self.azimuth_value_label)
        layout.addLayout(azimuth_layout)
        
        # 仰角控制
        elevation_layout = QHBoxLayout()
        elevation_layout.addWidget(QLabel("仰角:"))
        self.elevation_slider = QSlider(Qt.Horizontal)
        self.elevation_slider.setMinimum(-90)
        self.elevation_slider.setMaximum(90)
        self.elevation_slider.setValue(30)
        self.elevation_slider.valueChanged.connect(self._on_elevation_changed)
        elevation_layout.addWidget(self.elevation_slider)
        self.elevation_value_label = QLabel("30°")
        elevation_layout.addWidget(self.elevation_value_label)
        layout.addLayout(elevation_layout)
        
        # 中心位置控制组
        center_group = QGroupBox("显示中心位置")
        center_layout = QVBoxLayout(center_group)
        
        # X轴中心控制
        center_x_layout = QHBoxLayout()
        center_x_layout.addWidget(QLabel("X轴:"))
        self.center_x_slider = QSlider(Qt.Horizontal)
        self.center_x_slider.setMinimum(-100)
        self.center_x_slider.setMaximum(100)
        self.center_x_slider.setValue(0)
        self.center_x_slider.valueChanged.connect(self._on_center_x_changed)
        center_x_layout.addWidget(self.center_x_slider)
        self.center_x_value_label = QLabel("0.0")
        center_x_layout.addWidget(self.center_x_value_label)
        center_layout.addLayout(center_x_layout)
        
        # Y轴中心控制
        center_y_layout = QHBoxLayout()
        center_y_layout.addWidget(QLabel("Y轴:"))
        self.center_y_slider = QSlider(Qt.Horizontal)
        self.center_y_slider.setMinimum(-100)
        self.center_y_slider.setMaximum(100)
        self.center_y_slider.setValue(0)
        self.center_y_slider.valueChanged.connect(self._on_center_y_changed)
        center_y_layout.addWidget(self.center_y_slider)
        self.center_y_value_label = QLabel("0.0")
        center_y_layout.addWidget(self.center_y_value_label)
        center_layout.addLayout(center_y_layout)
        
        # Z轴中心控制
        center_z_layout = QHBoxLayout()
        center_z_layout.addWidget(QLabel("Z轴:"))
        self.center_z_slider = QSlider(Qt.Horizontal)
        self.center_z_slider.setMinimum(-100)
        self.center_z_slider.setMaximum(100)
        self.center_z_slider.setValue(0)
        self.center_z_slider.valueChanged.connect(self._on_center_z_changed)
        center_z_layout.addWidget(self.center_z_slider)
        self.center_z_value_label = QLabel("0.0")
        center_z_layout.addWidget(self.center_z_value_label)
        center_layout.addLayout(center_z_layout)
        
        # 中心位置控制按钮
        center_btn_layout = QHBoxLayout()
        reset_center_btn = QPushButton("重置中心")
        reset_center_btn.clicked.connect(self.reset_center_requested.emit)
        center_btn_layout.addWidget(reset_center_btn)
        
        auto_center_btn = QPushButton("自动居中")
        auto_center_btn.clicked.connect(self.auto_center_requested.emit)
        center_btn_layout.addWidget(auto_center_btn)
        center_layout.addLayout(center_btn_layout)
        
        layout.addWidget(center_group)
        
        # 相机控制按钮
        control_btn_layout = QHBoxLayout()
        reset_camera_btn = QPushButton("重置相机")
        reset_camera_btn.clicked.connect(self.reset_camera_requested.emit)
        control_btn_layout.addWidget(reset_camera_btn)
        layout.addLayout(control_btn_layout)
        
        # 相机预设按钮
        preset_group = QGroupBox("视角预设")
        preset_group_layout = QVBoxLayout(preset_group)
        
        preset_row1 = QHBoxLayout()
        for name, text in [("front", "正面"), ("side", "侧面"), ("top", "顶部")]:
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, n=name: self.preset_requested.emit(n))
            preset_row1.addWidget(btn)
        preset_group_layout.addLayout(preset_row1)
        
        preset_row2 = QHBoxLayout()
        for name, text in [("isometric", "等轴测"), ("bottom", "底部"), ("back", "背面")]:
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, n=name: self.preset_requested.emit(n))
            preset_row2.addWidget(btn)
        preset_group_layout.addLayout(preset_row2)
        
        layout.addWidget(preset_group)
    
    def _on_distance_changed(self, value):
        """距离滑块变化"""
        distance = value / 10.0
        self.distance_value_label.setText(f"{distance:.1f}")
        self.distance_changed.emit(distance)
    
    def _on_azimuth_changed(self, value):
        """方位角滑块变化"""
        self.azimuth_value_label.setText(f"{value}°")
        self.azimuth_changed.emit(value)
    
    def _on_elevation_changed(self, value):
        """仰角滑块变化"""
        self.elevation_value_label.setText(f"{value}°")
        self.elevation_changed.emit(value)
    
    def _on_center_x_changed(self, value):
        """X轴中心滑块变化"""
        center_x = value / 10.0
        self.center_x_value_label.setText(f"{center_x:.1f}")
        self.center_x_changed.emit(center_x)
    
    def _on_center_y_changed(self, value):
        """Y轴中心滑块变化"""
        center_y = value / 10.0
        self.center_y_value_label.setText(f"{center_y:.1f}")
        self.center_y_changed.emit(center_y)
    
    def _on_center_z_changed(self, value):
        """Z轴中心滑块变化"""
        center_z = value / 10.0
        self.center_z_value_label.setText(f"{center_z:.1f}")
        self.center_z_changed.emit(center_z)
    
    def _on_test_clicked(self):
        """测试按钮点击"""
        self.test_requested.emit(self.spibbox.value(), self.spibbox3.value(), self.spibbox2.value())


class MaterialControlPanel(QGroupBox):
    """材质控制面板"""
    
    material_type_changed = Signal(str)
    material_properties_changed = Signal()
    material_preset_applied = Signal(str)
    edges_toggled = Signal(bool)
    wireframe_toggled = Signal(bool)
    grid_toggled = Signal(bool)
    axis_toggled = Signal(bool)
    
    def __init__(self):
        super().__init__("材质控制")
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 材质类型选择
        material_type_layout = QHBoxLayout()
        material_type_layout.addWidget(QLabel("材质类型:"))
        self.material_type_combo = QComboBox()
        self.material_type_combo.addItems([
            "Default", "Metallic", "Plastic", "Glass",
            "Matte", "Glossy", "Skin", "Robot"
        ])
        self.material_type_combo.currentTextChanged.connect(self.material_type_changed.emit)
        material_type_layout.addWidget(self.material_type_combo)
        layout.addLayout(material_type_layout)
        
        # 材质属性控制
        # 光泽度控制
        glossiness_layout = QHBoxLayout()
        glossiness_layout.addWidget(QLabel("光泽度:"))
        self.glossiness_slider = QSlider(Qt.Horizontal)
        self.glossiness_slider.setMinimum(0)
        self.glossiness_slider.setMaximum(100)
        self.glossiness_slider.setValue(50)
        self.glossiness_slider.valueChanged.connect(lambda: self.material_properties_changed.emit())
        self.glossiness_label = QLabel("50%")
        self.glossiness_slider.valueChanged.connect(
            lambda v: self.glossiness_label.setText(f"{v}%"))
        glossiness_layout.addWidget(self.glossiness_slider)
        glossiness_layout.addWidget(self.glossiness_label)
        layout.addLayout(glossiness_layout)
        
        # 金属度控制
        metalness_layout = QHBoxLayout()
        metalness_layout.addWidget(QLabel("金属度:"))
        self.metalness_slider = QSlider(Qt.Horizontal)
        self.metalness_slider.setMinimum(0)
        self.metalness_slider.setMaximum(100)
        self.metalness_slider.setValue(0)
        self.metalness_slider.valueChanged.connect(lambda: self.material_properties_changed.emit())
        self.metalness_label = QLabel("0%")
        self.metalness_slider.valueChanged.connect(
            lambda v: self.metalness_label.setText(f"{v}%"))
        metalness_layout.addWidget(self.metalness_slider)
        metalness_layout.addWidget(self.metalness_label)
        layout.addLayout(metalness_layout)
        
        # 透明度控制
        transparency_layout = QHBoxLayout()
        transparency_layout.addWidget(QLabel("透明度:"))
        self.transparency_slider = QSlider(Qt.Horizontal)
        self.transparency_slider.setMinimum(0)
        self.transparency_slider.setMaximum(100)
        self.transparency_slider.setValue(0)
        self.transparency_slider.valueChanged.connect(lambda: self.material_properties_changed.emit())
        self.transparency_label = QLabel("0%")
        self.transparency_slider.valueChanged.connect(
            lambda v: self.transparency_label.setText(f"{v}%"))
        transparency_layout.addWidget(self.transparency_slider)
        transparency_layout.addWidget(self.transparency_label)
        layout.addLayout(transparency_layout)
        
        # 材质预设按钮
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("预设:"))
        
        for preset_name, text in [("chrome", "铬合金"), ("gold", "黄金"), ("plastic", "塑料")]:
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, name=preset_name: self.material_preset_applied.emit(name))
            preset_layout.addWidget(btn)
        layout.addLayout(preset_layout)
        
        # 显示选项
        display_layout = QVBoxLayout()
        display_layout.addWidget(QLabel("显示选项:"))
        
        # 第一行：边框和线框
        display_row1 = QHBoxLayout()
        self.show_edges_checkbox = QPushButton("显示边框")
        self.show_edges_checkbox.setCheckable(True)
        self.show_edges_checkbox.setChecked(False)
        self.show_edges_checkbox.clicked.connect(self.edges_toggled.emit)
        display_row1.addWidget(self.show_edges_checkbox)
        
        self.wireframe_checkbox = QPushButton("线框模式")
        self.wireframe_checkbox.setCheckable(True)
        self.wireframe_checkbox.setChecked(False)
        self.wireframe_checkbox.clicked.connect(self.wireframe_toggled.emit)
        display_row1.addWidget(self.wireframe_checkbox)
        display_layout.addLayout(display_row1)
        
        # 第二行：网格和坐标轴
        display_row2 = QHBoxLayout()
        self.show_grid_checkbox = QPushButton("显示网格")
        self.show_grid_checkbox.setCheckable(True)
        self.show_grid_checkbox.setChecked(False)
        self.show_grid_checkbox.clicked.connect(self.grid_toggled.emit)
        display_row2.addWidget(self.show_grid_checkbox)
        
        self.show_axis_checkbox = QPushButton("显示坐标轴")
        self.show_axis_checkbox.setCheckable(True)
        self.show_axis_checkbox.setChecked(False)
        self.show_axis_checkbox.clicked.connect(self.axis_toggled.emit)
        display_row2.addWidget(self.show_axis_checkbox)
        display_layout.addLayout(display_row2)
        
        layout.addLayout(display_layout)


class ColorControlPanel(QGroupBox):
    """颜色控制面板"""
    
    link_color_changed = Signal(QColor)
    joint_color_changed = Signal(QColor)
    
    def __init__(self):
        super().__init__("颜色控制")
        self.link_color = QColor(180, 180, 180, 255)
        self.joint_color = QColor(255, 0, 0, 255)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 链接颜色
        link_color_layout = QHBoxLayout()
        link_color_layout.addWidget(QLabel("链接颜色:"))
        self.link_color_button = QPushButton("选择")
        self.link_color_button.clicked.connect(self._choose_link_color)
        link_color_layout.addWidget(self.link_color_button)
        layout.addLayout(link_color_layout)
        
        # 关节颜色
        joint_color_layout = QHBoxLayout()
        joint_color_layout.addWidget(QLabel("关节颜色:"))
        self.joint_color_button = QPushButton("选择")
        self.joint_color_button.clicked.connect(self._choose_joint_color)
        joint_color_layout.addWidget(self.joint_color_button)
        layout.addLayout(joint_color_layout)
    
    def _choose_link_color(self):
        """选择链接颜色"""
        color = QColorDialog.getColor(self.link_color, self, "选择链接颜色")
        if color.isValid():
            self.link_color = color
            self.link_color_changed.emit(color)
    
    def _choose_joint_color(self):
        """选择关节颜色"""
        color = QColorDialog.getColor(self.joint_color, self, "选择关节颜色")
        if color.isValid():
            self.joint_color = color
            self.joint_color_changed.emit(color)
