# URDF整合查看器 - 模块化版本

## 概述

这是URDF整合查看器的模块化重构版本。原来的单一文件`urdf_integrated_viewer.py`（3500+行）已被重构为清晰的模块化架构，提高了代码的可维护性、可扩展性和可重用性。

## 模块化架构

### 目录结构

```
src/
├── __init__.py                 # 主包初始化
├── core/                       # 核心业务逻辑
│   ├── __init__.py
│   ├── urdf_parser.py         # URDF解析、路径转换、关节分析
│   ├── kinematics.py          # 运动学计算
│   └── loader.py              # 后台加载线程
├── ui/                        # 用户界面模块
│   ├── __init__.py
│   ├── main_window.py         # 主窗口
│   ├── control_panels.py      # 控制面板组件
│   └── joint_controls.py      # 关节控制界面
├── rendering/                 # 3D渲染模块
│   ├── __init__.py
│   ├── mesh_renderer.py       # 网格渲染器
│   └── camera_controller.py   # 相机控制器
└── utils/                     # 工具和常量
    ├── __init__.py
    └── constants.py           # 应用常量和配置
```

### 模块说明

#### 1. 核心模块 (`src/core/`)

- **`urdf_parser.py`**: 
  - `URDFPathConverter`: ROS包路径转换
  - `URDFJointAnalyzer`: 关节信息分析
  - `FingerGroupAnalyzer`: 智能手指分组

- **`kinematics.py`**:
  - `SimpleTransformCalculator`: 前向运动学计算

- **`loader.py`**:
  - `URDFLoadWorker`: 后台URDF文件加载线程

#### 2. 用户界面模块 (`src/ui/`)

- **`main_window.py`**:
  - `URDFIntegratedViewer`: 主窗口类

- **`control_panels.py`**:
  - `FileControlPanel`: 文件操作控制面板
  - `CameraControlPanel`: 相机控制面板
  - `MaterialControlPanel`: 材质控制面板
  - `ColorControlPanel`: 颜色控制面板

- **`joint_controls.py`**:
  - `JointControlManager`: 关节控制管理器

#### 3. 渲染模块 (`src/rendering/`)

- **`mesh_renderer.py`**:
  - `MeshRenderer`: 3D网格渲染器

- **`camera_controller.py`**:
  - `CameraController`: 3D视图相机控制器

#### 4. 工具模块 (`src/utils/`)

- **`constants.py`**:
  - 应用配置常量
  - 默认颜色设置
  - 材质预设
  - 相机预设

## 使用方法

### 启动应用程序

```bash
python main.py
```

### 导入模块使用

```python
# 导入主窗口
from src import URDFIntegratedViewer

# 导入特定组件
from src.core import URDFJointAnalyzer, SimpleTransformCalculator
from src.rendering import MeshRenderer, CameraController
from src.ui import FileControlPanel, JointControlManager
```

## 主要改进

### 1. 代码组织
- **分离关注点**: 每个模块专注于特定功能
- **清晰的依赖关系**: 模块间依赖明确且最小化
- **可重用组件**: 各模块可独立使用和测试

### 2. 可维护性
- **模块化设计**: 便于理解和修改特定功能
- **文档完善**: 每个模块和类都有详细文档
- **错误处理**: 改进的错误处理和日志记录

### 3. 可扩展性
- **插件化架构**: 易于添加新的渲染器或控制面板
- **配置驱动**: 通过配置文件控制应用行为
- **接口标准化**: 清晰的接口定义便于扩展

### 4. 性能优化
- **缓存机制**: 网格加载和变换计算的缓存
- **异步加载**: 后台线程加载URDF文件
- **更新优化**: 防抖动的视图更新机制

## 功能特性

### 核心功能
- ✅ URDF文件加载和解析
- ✅ 3D模型可视化
- ✅ 智能关节分组控制
- ✅ 实时运动学计算
- ✅ 材质和视觉效果控制

### 用户界面
- ✅ 直观的控制面板
- ✅ 相机控制（距离、角度、中心）
- ✅ 材质预设和自定义
- ✅ 预设动作和随机姿态
- ✅ 图片保存功能

### 技术特性
- ✅ 多线程加载
- ✅ 性能优化的渲染
- ✅ 错误处理和恢复
- ✅ 配置化设计

## 依赖要求

```
PySide6>=6.0.0
pyqtgraph>=0.12.0
numpy>=1.20.0
trimesh>=3.9.0
numpy-stl>=2.16.0
urdfpy>=0.0.22
```

## 开发指南

### 添加新的控制面板

1. 在`src/ui/control_panels.py`中创建新的面板类
2. 继承`QGroupBox`并实现所需的信号
3. 在`main_window.py`中集成新面板

### 添加新的渲染功能

1. 在`src/rendering/`中创建新的渲染器模块
2. 实现标准的渲染接口
3. 在`MeshRenderer`中集成新功能

### 修改配置

编辑`src/utils/constants.py`文件来修改：
- 默认颜色
- 材质预设
- 相机预设
- 应用配置

## 与原版本的兼容性

模块化版本保持了与原版本相同的功能和用户体验，主要变化在于：

1. **代码组织**: 从单文件变为模块化结构
2. **导入方式**: 使用新的模块导入路径
3. **配置管理**: 集中的配置管理
4. **错误处理**: 改进的错误处理机制

## 未来计划

- [ ] 添加插件系统
- [ ] 支持更多3D格式
- [ ] 增加动画录制功能
- [ ] 添加物理仿真集成
- [ ] 支持多机器人显示

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 遵循模块化设计原则
4. 添加适当的测试
5. 提交Pull Request

## 许可证

与原项目保持相同的许可证。
