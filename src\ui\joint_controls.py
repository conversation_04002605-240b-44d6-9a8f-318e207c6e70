# -*- coding: utf-8 -*-
"""
关节控制模块

提供关节控制界面的创建和管理
"""

import random
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                             QLabel, QSlider, QPushButton, QTabWidget)
from PySide6.QtCore import Qt, Signal
from typing import Dict, Any


class JointControlManager(QWidget):
    """关节控制管理器"""
    
    joint_value_changed = Signal(str, float)  # 关节名称, 值
    finger_group_changed = Signal(str, float)  # 分组名称, 值
    random_pose_requested = Signal()
    reset_pose_requested = Signal()
    
    def __init__(self):
        super().__init__()
        self.finger_group_sliders = {}
        self.finger_group_vars = {}
        self.individual_joint_sliders = {}
        self.individual_joint_vars = {}
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.layout = QVBoxLayout(self)
        # 控件将在create_joint_controls中动态创建
    
    def create_joint_controls(self, finger_groups: Dict[str, Any]):
        """
        创建关节控制界面
        
        Args:
            finger_groups: 手指分组信息
        """
        # 清除现有控件
        self.clear_controls()
        
        # 创建标签页控件
        tab_widget = QTabWidget()
        self.layout.addWidget(tab_widget)
        
        # 手指分组控制标签页
        finger_tab = QWidget()
        finger_layout = QVBoxLayout(finger_tab)
        
        # 创建手指分组控制
        finger_group_widget = self._create_finger_group_controls(finger_groups['finger_groups'])
        finger_layout.addWidget(finger_group_widget)
        
        # 添加预设动作按钮
        preset_widget = self._create_preset_controls()
        finger_layout.addWidget(preset_widget)
        
        finger_layout.addStretch()
        tab_widget.addTab(finger_tab, "手指控制")
        
        # 独立关节控制标签页
        if finger_groups['individual_joints']:
            joint_tab = QWidget()
            joint_layout = QVBoxLayout(joint_tab)
            
            joint_group_widget = self._create_individual_joint_controls(finger_groups['individual_joints'])
            joint_layout.addWidget(joint_group_widget)
            joint_layout.addStretch()
            
            tab_widget.addTab(joint_tab, "独立关节")
    
    def _create_finger_group_controls(self, finger_groups: Dict) -> QGroupBox:
        """创建手指分组控制"""
        group_box = QGroupBox("手指分组控制")
        layout = QVBoxLayout(group_box)
        
        for group_key, group_config in finger_groups.items():
            # 创建分组控制
            group_layout = QHBoxLayout()
            
            # 分组名称标签
            name_label = QLabel(f"{group_config['name']}:")
            name_label.setMinimumWidth(80)
            group_layout.addWidget(name_label)
            
            # 分组滑块
            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(0)
            slider.setMaximum(100)
            slider.setValue(0)
            slider.valueChanged.connect(
                lambda value, key=group_key: self._on_finger_group_changed(key, value)
            )
            group_layout.addWidget(slider)
            
            # 数值标签
            value_label = QLabel("0%")
            value_label.setMinimumWidth(40)
            group_layout.addWidget(value_label)
            
            # 存储控件引用
            self.finger_group_sliders[group_key] = {
                'slider': slider,
                'label': value_label,
                'config': group_config
            }
            
            # 连接标签更新
            slider.valueChanged.connect(
                lambda value, label=value_label: label.setText(f"{value}%")
            )
            
            layout.addLayout(group_layout)
            
            # 添加描述标签
            if group_config.get('description'):
                desc_label = QLabel(f"  {group_config['description']}")
                desc_label.setStyleSheet("color: gray; font-size: 10px;")
                layout.addWidget(desc_label)
        
        return group_box
    
    def _create_individual_joint_controls(self, individual_joints: Dict) -> QGroupBox:
        """创建独立关节控制"""
        group_box = QGroupBox("独立关节控制")
        layout = QVBoxLayout(group_box)
        
        for joint_name, joint_data in individual_joints.items():
            joint_info = joint_data['info']
            
            # 创建关节控制
            joint_layout = QHBoxLayout()
            
            # 关节名称标签
            name_label = QLabel(f"{joint_name}:")
            name_label.setMinimumWidth(120)
            joint_layout.addWidget(name_label)
            
            # 关节滑块
            slider = QSlider(Qt.Horizontal)
            
            # 设置滑块范围
            lower = joint_info['lower']
            upper = joint_info['upper']
            range_size = upper - lower
            
            if range_size > 0:
                # 使用1000个刻度来提供足够的精度
                slider.setMinimum(0)
                slider.setMaximum(1000)
                # 设置初始值为范围中点
                initial_ratio = 0.5
                slider.setValue(int(initial_ratio * 1000))
            else:
                slider.setMinimum(0)
                slider.setMaximum(0)
                slider.setValue(0)
            
            slider.sliderReleased.connect(
                lambda value, name=joint_name: self._on_individual_joint_changed(name, value)
            )
            joint_layout.addWidget(slider)
            
            # 数值标签
            initial_value = lower + (upper - lower) * 0.5 if range_size > 0 else lower
            value_label = QLabel(f"{initial_value:.3f}")
            value_label.setMinimumWidth(60)
            joint_layout.addWidget(value_label)
            
            # 存储控件引用
            self.individual_joint_sliders[joint_name] = {
                'slider': slider,
                'label': value_label,
                'info': joint_info
            }
            
            layout.addLayout(joint_layout)
            
            # 添加范围信息标签
            range_label = QLabel(f"  范围: [{lower:.3f}, {upper:.3f}]")
            range_label.setStyleSheet("color: gray; font-size: 10px;")
            layout.addWidget(range_label)
        
        return group_box
    
    def _create_preset_controls(self) -> QGroupBox:
        """创建预设控制"""
        group_box = QGroupBox("预设动作")
        layout = QVBoxLayout(group_box)
        
        # 第一行按钮
        row1_layout = QHBoxLayout()
        
        # 重置姿态按钮
        reset_btn = QPushButton("重置姿态")
        reset_btn.clicked.connect(self.reset_pose_requested.emit)
        row1_layout.addWidget(reset_btn)
        
        # 随机姿态按钮
        random_btn = QPushButton("随机姿态")
        random_btn.clicked.connect(self.random_pose_requested.emit)
        row1_layout.addWidget(random_btn)
        
        layout.addLayout(row1_layout)
        
        # 第二行按钮 - 预设手势
        row2_layout = QHBoxLayout()
        
        # 握拳按钮
        fist_btn = QPushButton("握拳")
        fist_btn.clicked.connect(lambda: self._apply_preset_pose("fist"))
        row2_layout.addWidget(fist_btn)
        
        # 张开按钮
        open_btn = QPushButton("张开")
        open_btn.clicked.connect(lambda: self._apply_preset_pose("open"))
        row2_layout.addWidget(open_btn)
        
        # 指向按钮
        point_btn = QPushButton("指向")
        point_btn.clicked.connect(lambda: self._apply_preset_pose("point"))
        row2_layout.addWidget(point_btn)
        
        layout.addLayout(row2_layout)
        
        return group_box
    
    def _on_finger_group_changed(self, group_key: str, value: int):
        """手指分组滑块变化"""
        # 将滑块值(0-100)转换为比例(0.0-1.0)
        ratio = value / 100.0
        self.finger_group_changed.emit(group_key, ratio)
    
    def _on_individual_joint_changed(self, joint_name: str, slider_value: int):
        """独立关节滑块变化"""
        if joint_name in self.individual_joint_sliders:
            joint_data = self.individual_joint_sliders[joint_name]
            joint_info = joint_data['info']
            
            # 将滑块值转换为实际关节值
            ratio = slider_value / 1000.0
            lower = joint_info['lower']
            upper = joint_info['upper']
            actual_value = lower + (upper - lower) * ratio
            
            # 更新标签
            joint_data['label'].setText(f"{actual_value:.3f}")
            
            # 发送信号
            self.joint_value_changed.emit(joint_name, actual_value)
    
    def _apply_preset_pose(self, pose_name: str):
        """应用预设姿态"""
        if pose_name == "fist":
            # 握拳：所有手指弯曲
            for group_key in self.finger_group_sliders:
                if 'bend' in group_key or group_key in ['index', 'middle', 'ring', 'pinky']:
                    self.finger_group_sliders[group_key]['slider'].setValue(80)
        elif pose_name == "open":
            # 张开：所有手指伸直
            for group_key in self.finger_group_sliders:
                self.finger_group_sliders[group_key]['slider'].setValue(0)
        elif pose_name == "point":
            # 指向：只有食指伸直，其他弯曲
            for group_key in self.finger_group_sliders:
                if group_key == 'index':
                    self.finger_group_sliders[group_key]['slider'].setValue(0)
                elif 'bend' in group_key or group_key in ['middle', 'ring', 'pinky']:
                    self.finger_group_sliders[group_key]['slider'].setValue(70)
    
    def apply_random_pose(self):
        """应用随机姿态"""
        # 随机设置手指分组
        for group_key, group_data in self.finger_group_sliders.items():
            random_value = random.randint(0, 100)
            group_data['slider'].setValue(random_value)
        
        # 随机设置独立关节
        for joint_name, joint_data in self.individual_joint_sliders.items():
            random_value = random.randint(0, 1000)
            joint_data['slider'].setValue(random_value)
    
    def reset_all_joints(self):
        """重置所有关节"""
        # 重置手指分组
        for group_data in self.finger_group_sliders.values():
            group_data['slider'].setValue(0)
        
        # 重置独立关节到中间位置
        for joint_data in self.individual_joint_sliders.values():
            joint_data['slider'].setValue(500)  # 中间位置
    
    def clear_controls(self):
        """清除所有控件"""
        while self.layout.count():
            item = self.layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        self.finger_group_sliders.clear()
        self.finger_group_vars.clear()
        self.individual_joint_sliders.clear()
        self.individual_joint_vars.clear()
